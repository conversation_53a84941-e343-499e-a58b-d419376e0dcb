/**
 * Shop Filters Modal JavaScript
 * Magaza sayfasi filtreler modal fonksiyonlari
 */

(function($) {
    'use strict';

    // DOM hazir oldugunda calistir
    $(document).ready(function() {
        initShopFilters();
    });

    function initShopFilters() {
        const modal = $('#shop-filters-modal');
        const toggleBtn = $('#shop-filters-toggle');
        const closeBtn = $('#shop-filters-close');
        const clearBtn = $('#clear-filters');
        const form = $('#shop-filters-form');

        // Modal acma
        toggleBtn.on('click', function(e) {
            e.preventDefault();
            openModal();
        });

        // Modal kapama - X butonu
        closeBtn.on('click', function(e) {
            e.preventDefault();
            closeModal();
        });

        // Modal kapama - arka plan tiklama
        modal.on('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        // ESC tusuna basinca modal kapat
        $(document).on('keydown', function(e) {
            if (e.key === 'Escape' && modal.is(':visible')) {
                closeModal();
            }
        });

        // Filtreleri temizle
        clearBtn.on('click', function(e) {
            e.preventDefault();
            clearFilters();
        });

        // Form gonderimi
        form.on('submit', function(e) {
            e.preventDefault();
            applyFilters();
        });

        // Checkbox degisikliklerini dinle
        form.find('input[type="checkbox"]').on('change', function() {
            updateFilterCount();
        });

        // Fiyat input degisikliklerini dinle
        form.find('input[type="number"]').on('input', function() {
            updateFilterCount();
        });

        // Sayfa yuklendiginde aktif filtre sayisini guncelle
        updateFilterCount();
    }

    function openModal() {
        const modal = $('#shop-filters-modal');
        modal.fadeIn(300);
        $('body').addClass('modal-open');
        
        // Modal acildiginda ilk input'a focus ver
        setTimeout(function() {
            modal.find('input:first').focus();
        }, 350);
    }

    function closeModal() {
        const modal = $('#shop-filters-modal');
        modal.fadeOut(300);
        $('body').removeClass('modal-open');
    }

    function clearFilters() {
        const form = $('#shop-filters-form');
        
        // Tum checkbox'lari temizle
        form.find('input[type="checkbox"]').prop('checked', false);
        
        // Fiyat inputlarini temizle
        form.find('input[type="number"]').val('');
        
        // Filtre sayisini guncelle
        updateFilterCount();
        
        // Filtreleri uygula (temiz hali)
        applyFilters();
    }

    function applyFilters() {
        const form = $('#shop-filters-form');
        const params = new URLSearchParams();

        // Mevcut URL parametrelerini al
        const currentParams = new URLSearchParams(window.location.search);

        // Sayfa numarasini sifirla (filtreleme sonrasi ilk sayfaya don)
        currentParams.delete('paged');

        // Fiyat filtrelerini ekle
        const minPrice = form.find('input[name="min_price"]').val();
        const maxPrice = form.find('input[name="max_price"]').val();

        if (minPrice && minPrice.trim() !== '') {
            params.set('min_price', minPrice);
        }

        if (maxPrice && maxPrice.trim() !== '') {
            params.set('max_price', maxPrice);
        }

        // Checkbox array'lerini isle
        const checkboxGroups = ['product_cat', 'product_tag', 'stock_status'];
        checkboxGroups.forEach(function(group) {
            const checkedValues = [];
            form.find('input[name="' + group + '[]"]:checked').each(function() {
                checkedValues.push($(this).val());
            });

            if (checkedValues.length > 0) {
                params.set(group, checkedValues.join(','));
            }
        });

        // Diger mevcut parametreleri koru (arama, kategori vb.)
        for (let [key, value] of currentParams.entries()) {
            if (!['min_price', 'max_price', 'product_cat', 'product_tag', 'stock_status', 'paged'].includes(key)) {
                params.set(key, value);
            }
        }

        // Yeni URL olustur ve yonlendir
        let newUrl = window.location.pathname;
        if (params.toString()) {
            newUrl += '?' + params.toString();
        }

        // Loading goster
        showLoading();

        // Modal'i kapat
        closeModal();

        // Sayfayi yenile
        window.location.href = newUrl;
    }

    function updateFilterCount() {
        const form = $('#shop-filters-form');
        let count = 0;

        // Checkbox'lari say
        count += form.find('input[type="checkbox"]:checked').length;

        // Fiyat inputlarini kontrol et
        const minPrice = form.find('input[name="min_price"]').val();
        const maxPrice = form.find('input[name="max_price"]').val();
        
        if (minPrice && minPrice.trim() !== '') count++;
        if (maxPrice && maxPrice.trim() !== '') count++;

        // Buton metnini guncelle
        const toggleBtn = $('#shop-filters-toggle');
        const originalText = 'Filtreler';
        
        if (count > 0) {
            toggleBtn.html('<i class="fas fa-filter"></i> ' + originalText + ' (' + count + ')');
            toggleBtn.addClass('has-filters');
        } else {
            toggleBtn.html('<i class="fas fa-filter"></i> ' + originalText);
            toggleBtn.removeClass('has-filters');
        }
    }

    function showLoading() {
        const toggleBtn = $('#shop-filters-toggle');
        toggleBtn.html('<i class="fas fa-spinner fa-spin"></i> Yukleniyor...');
        toggleBtn.prop('disabled', true);
    }

    // CSS sinifi ekle - modal acikken body scroll'unu engelle
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            body.modal-open {
                overflow: hidden;
            }
            .shop-filters-toggle.has-filters {
                background: #28a745 !important;
            }
            .shop-filters-toggle.has-filters:hover {
                background: #218838 !important;
            }
        `)
        .appendTo('head');

})(jQuery);
